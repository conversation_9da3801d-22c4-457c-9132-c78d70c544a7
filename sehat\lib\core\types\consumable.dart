class Consumable {
  final String id;
  final String name;
  final String pic;
  final double calorie;
  final double? fat;
  final double? carb;
  final double? sugar;
  final double? protein;
  final double? sodium;
  final double? fiber;

  Consumable({
    required this.id,
    required this.name,
    required this.pic,
    required this.calorie,
    required this.fat,
    required this.carb,
    required this.sugar,
    required this.protein,
    required this.sodium,
    required this.fiber,
  });

  factory Consumable.fromJson(Map<String, dynamic> json) {
    return Consumable(
      id: json['id'],
      name: json['name'],
      pic: json['pic'],
      calorie: json['calorie'],
      fat: json['fat'],
      carb: json['carb'],
      sugar: json['sugar'],
      protein: json['protein'],
      sodium: json['sodium'],
      fiber: json['fiber'],
    );
  }
}
