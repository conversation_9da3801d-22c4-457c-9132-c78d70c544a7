class UserActivity {
  final String id;
  final String name;
  final int calorie;
  final int duration;

  final String uid;
  final DateTime ts;

  UserActivity({
    required this.id,
    required this.name,
    required this.calorie,
    required this.duration,
    required this.uid,
    required this.ts,
  });

  factory UserActivity.fromJson(Map<String, dynamic> json) {
    return UserActivity(
      id: json['id'],
      name: json['name'],
      calorie: json['calorie'],
      duration: json['duration'],
      uid: json['uid'],
      ts: DateTime.fromMillisecondsSinceEpoch(json['ts']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'calorie': calorie,
      'duration': duration,
      'uid': uid,
      'ts': ts.millisecondsSinceEpoch,
    };
  }

}
