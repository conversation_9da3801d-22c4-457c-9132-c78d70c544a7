import 'package:sehat/c.dart';

class SehatConsumption extends SehatPart {
  SehatConsumption(super.sehat);

  
  // -------------------------------------------- def
  Future<List<Activity>> consumableList() async {
    return [
      Activity(id: 'id', name: 'sex', calorie: 300, duration: Duration(hours: 2))
    ];
  }

  // -------------------------------------------- user consumption
  Future<List<UserConsumtion>> userConsumptionList(String uid) async {
    return [
      
    ];
  }

  Future<void> userConsumptionAdd(String consumableId, double amount, String unit) async {

  }

  Future<void> userConsumptionDel(String id) async {


  }
}
