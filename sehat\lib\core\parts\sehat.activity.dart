import 'package:sehat/c.dart';

class SehatActivity extends SehatPart {
  SehatActivity(super.sehat);

  // -------------------------------------------- def
  Future<List<Activity>> activitiesList() async {
    return [
      Activity(id: 'id', name: 'sex', calorie: 300, duration: Duration(hours: 2))
    ];
  }

  // -------------------------------------------- user activity
  Future<List<UserActivity>> userActivitiesList(String uid) async {
    return [
      UserActivity(id: 'id', name: 'user sex', calorie: 300, duration: 300, uid: uid, ts: DateTime.now())
    ];
  }

  Future<void> userActivityAdd(String activityId, Duration duration) async {

  }

  Future<void> userActivityAddByImage(String base64Image) async {

  }

  Future<void> userActivityDel(String id) async {

  }


}
