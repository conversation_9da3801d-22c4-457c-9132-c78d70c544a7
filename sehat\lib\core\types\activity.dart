class Activity {
  final String id;
  final String name;
  final int calorie;
  final Duration duration;

  Activity({
    required this.id,
    required this.name,
    required this.calorie,
    required this.duration,
  });

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'],
      name: json['name'],
      calorie: json['calorie'],
      duration: Duration(milliseconds: json['duration']) ,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'calorie': calorie,
      'duration': duration.inMilliseconds,
    };
  }
}
